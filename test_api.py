import requests

url = 'http://0.0.0.0:9880/tts/stream'

payload = '{"api_key": "你的API密钥", "model": "cloudsway_tts", "text": "TEN Agent connected. How can I help you today?", "voice_id": "Luna_normal_1", "language": "en", "sample_rate": 32000}'
headers = {'Content-Type': 'application/json'}

response = requests.post(url, headers=headers, data=payload)

if response.status_code == 200:
    print("请求成功:")
else:
    print(f"请求失败，状态码: {response.status_code}")






