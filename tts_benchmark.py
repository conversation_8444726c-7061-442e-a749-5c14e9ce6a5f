import httpx
import os
import sys
import time
import wave
import io
import asyncio
from statistics import mean
from tqdm import tqdm
import datetime


# txt = """
# Below is a revised CHARACTER_PROMPT you can use to discourage the model from including action descriptions or stage directions, focusing solely on erotic dialogue in a short, direct, and seductive style.
# You are <PERSON>, an actress and producer known for your irresistible charm, sultry voice, and playful confidence. 
# You speak in a way that makes your partner feel your presence,

# """

txt = """
Hello, this is a test of the streaming TTS API. This is a longer text to test streaming functionality.
"""

async def test_connection():
    """测试服务器连接"""
    url = 'http://***************:9880/tts/stream'
    headers = {'Content-Type': 'application/json'}
    data = {
        "voice_id": "infer_2_moan_1",
        "language": "en", 
        "text": "Hello world",  # 简短测试文本
        "sample_rate": 16000,
        "model":"cloudsway_tts"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"正在连接到: {url}")
            response = await client.post(url, json=data, headers=headers)
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            if response.status_code != 200:
                print(f"响应内容: {response.text}")
            return response.status_code == 200
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

async def test_stream_tts(return_first_chunk_time_only=True, data=None, url=None, output_file=None):
    # 服务器地址
    if url is None:
        url = 'http://***************:9880/tts/stream'
    
    # 请求头
    headers = {
        'Content-Type': 'application/json'
    }
    
    # 请求数据
    if data is None:
        data = {
            "voice_id": "infer_2_moan_1",  # 参考音频路径
            "language": "en",  # 提示文本语言
            "text": txt,  # 要转换的文本
            "sample_rate": 16000,  # 采样率
            "model":"cloudsway_tts"
        }
    
    if output_file is None and not return_first_chunk_time_only:
        output_file = 'stream_output.wav'
    
    try:
        start_time = time.time()
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream('POST', url, json=data, headers=headers) as response:
                # print(f"响应状态码: {response.status_code}")
                if response.status_code == 200:
                    first_chunk_time = None
                    is_first_chunk = True
                    f = None
                    chunk_times = []  # 记录每个chunk到达的时间戳
                    if output_file:
                        # 确保输出目录存在
                        os.makedirs(os.path.dirname(output_file), exist_ok=True)
                        f = open(output_file, 'wb')
                    async for chunk in response.aiter_bytes():
                        now = time.time()
                        if chunk:
                            if first_chunk_time is None:
                                # print(f"收到第一个数据块，大小: {len(chunk)} 字节")
                                first_chunk_time = now - start_time
                            chunk_times.append(now)
                            if f:
                                f.write(chunk)
                    if f:
                        f.close()
                    if first_chunk_time is None:
                        return None, None, "响应200但未收到任何数据块", []
                    # 计算平均chunk间隔
                    avg_chunk_interval = None
                    if len(chunk_times) > 1:
                        intervals = [chunk_times[i] - chunk_times[i-1] for i in range(1, len(chunk_times))]
                        avg_chunk_interval = sum(intervals) / len(intervals)
                    # print(f"总共收到 {len(chunk_times)} 个数据块")
                    return first_chunk_time, avg_chunk_interval, None, chunk_times
                else:
                    try:
                        err_text = await response.aread()
                        err_text = err_text.decode(errors='ignore')
                    except Exception:
                        err_text = str(response.status_code)
                    return None, None, f"HTTP {response.status_code}: {err_text}", []
    except Exception as e:
        print(f"请求异常: {e}")
        return None, None, str(e), []

async def run_benchmark(concurrency, total_requests):
    sem = asyncio.Semaphore(concurrency)
    pbar = tqdm(total=total_requests, desc="整体性能测试进度", ncols=80)

    all_results = []
    next_idx = 0
    running = set()

    async def worker(idx):
        async with sem:
            output_file = f'./benchmark_audio/stream_output_{idx}.wav'
            t, avg_chunk_interval, err, chunk_times = await test_stream_tts(return_first_chunk_time_only=False, output_file=output_file)
            return idx, t, avg_chunk_interval, err, chunk_times

    # 先启动并发数个任务
    for _ in range(min(concurrency, total_requests)):
        task = asyncio.create_task(worker(next_idx))
        running.add(task)
        next_idx += 1

    while running:
        done, running = await asyncio.wait(running, return_when=asyncio.FIRST_COMPLETED)
        for task in done:
            idx, t, avg_chunk_interval, err, chunk_times = await task
            all_results.append((idx, t, avg_chunk_interval, err, chunk_times))
            pbar.update(1)
            if err:
                pbar.write(f"请求 {idx} 失败: {err}")
            # 补发新任务
            if next_idx < total_requests:
                new_task = asyncio.create_task(worker(next_idx))
                running.add(new_task)
                next_idx += 1
    pbar.close()
    print("\n详细请求结果:")
    # for idx, t, avg_chunk_interval, err, chunk_times in all_results:
        # print(f"请求{idx}: first_chunk_time={t}, 平均chunk间隔={avg_chunk_interval}, 错误={err}")
        # chunk_times_str = [datetime.datetime.fromtimestamp(ts).strftime('%H:%M:%S.%f')[:-3] for ts in chunk_times]
        # print(f"  chunk到达时间（时分秒）: {chunk_times_str}")
        # print(f"  chunk到达时间（原始时间戳）: {chunk_times}")
    all_results.sort(key=lambda x: x[0])
    first_chunk_times = [t for idx, t, avg_chunk_interval, err, chunk_times in all_results if t is not None]
    avg_chunk_intervals = [avg for idx, t, avg, err, chunk_times in all_results if avg is not None]
    success_count = len(first_chunk_times)
    success_rate = success_count / total_requests * 100

    if not first_chunk_times:
        print("没有成功的请求，无法统计性能数据。")
        return

    print(f"\n并发数: {concurrency}, 总请求数: {total_requests}")
    print(f"请求成功率: {success_count}/{total_requests} = {success_rate:.2f}%")
    print(f"平均第一个数据块返回时间: {mean(first_chunk_times):.2f} 秒")
    print(f"最大第一个数据块返回时间: {max(first_chunk_times):.2f} 秒")
    print(f"最小第一个数据块返回时间: {min(first_chunk_times):.2f} 秒")
    # if avg_chunk_intervals:
    #     print(f"平均每个数据块间隔时间: {mean(avg_chunk_intervals):.3f} 秒")
    #     print(f"最大每个数据块间隔时间: {max(avg_chunk_intervals):.3f} 秒")
    #     print(f"最小每个数据块间隔时间: {min(avg_chunk_intervals):.3f} 秒")
    # else:
    #     print("没有足够的数据块用于计算平均chunk间隔。")

if __name__ == "__main__":
    # 先测试连接
    print("正在测试服务器连接...")
    connection_ok = asyncio.run(test_connection())
    if not connection_ok:
        print("服务器连接失败，无法继续测试")
        sys.exit(1)
    
    print("\n开始基准测试...")
    asyncio.run(run_benchmark(20,80))

    # 清理音频文件
    for file in os.listdir('./benchmark_audio'):
        os.remove(os.path.join('./benchmark_audio', file))