import mysql.connector
from mysql.connector import errorcode

class DatabaseManager:
    def __init__(self, host='***************', user='admin', password='Cloudsway00@12Mk3', database='cloudsway_tts'):
        self.host = host
        self.user = user
        self.password = password
        self.database = database

        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )

        except mysql.connector.Error as err:
            if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
                print("Something is wrong with your user name or password")
            elif err.errno == errorcode.ER_BAD_DB_ERROR:
                print("Database does not exist")
            else:
                print(err)
                
if __name__ == "__main__":
    db = DatabaseManager()
    print(db.connection)
    db.connection.close()
