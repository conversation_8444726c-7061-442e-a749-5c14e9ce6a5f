import asyncio
import websockets
import json
import wave
import io
import time

async def test_tts_websocket():
    # WebSocket服务器地址
    uri = "wss://tts-websocket.xiaosuai.com/tts2"
    # uri = "ws://116.148.216.100:9880/tts"
    
    start_time = time.time()
    try:
        async with websockets.connect(uri, open_timeout=2) as websocket:
            print(f"连接成功，用时: {time.time() - start_time:.2f} 秒")
            # 准备测试数据
            test_data = {
                "model": "cloudsway_tts",
                "voice_id": "Luna_normal_1", 
                "language": "en",
                "text": "Hello, this is a test of the streaming TTS API. This is a longer text to test streaming functionality.",
                "sample_rate": 32000,
                "api_key": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************.ZHZDzehyTymCuwtYMFc75LMxN9CHEyLOqCu59ByS3IRYLI7P186CZNR8S2r2b41zfzIaCafXJlbAVCQQTovSzTLfaf87VsQE1gB8RDPWngMOysYF2wTIvL6T2wn-y5XuDtjiliU1jrEdWSLFm5hXcy8M-3mY680tMmlWxkHuw-cF-1ZxkDRF4ipF5JwDdwseuJtLM2j8pLv_ryJus-Bb5TYRS-opUbUGco6gZUHcJktZUhIuYC5h0L_rjbh5peX6A-c2xFJX7Wp1Bu8afBTZTAw7RJuzk9IUURWHYJm4BlNC3NGyFS6579bx73r682S2ST0WWn_WLZGW55q8UMAFKg"
            }
            
            print("发送TTS请求...")
            # 发送请求
            await websocket.send(json.dumps(test_data))
            
            # 接收音频数据
            audio_chunks = []
            start_time = time.time()
            
            while True:
                try:
                    # 设置超时时间为30秒
                    message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    
                    if isinstance(message, str):
                        # 处理错误消息
                        error_data = json.loads(message)
                        print(f"收到错误: {error_data}")
                        break
                    else:
                        # 处理音频数据
                        audio_chunks.append(message)
                        print(f"收到音频数据块，大小: {len(message)} 字节")
                        
                except asyncio.TimeoutError:
                    print("接收超时")
                    break
                except websockets.exceptions.ConnectionClosed:
                    print("连接已关闭")
                    break
            
            # 保存音频数据
            if audio_chunks:
                # 合并所有音频块
                complete_audio = b''.join(audio_chunks)
                
                # 保存为WAV文件
                output_filename = "ws_output.wav"
                with open(output_filename, 'wb') as f:
                    f.write(complete_audio)
                
                print(f"音频已保存到: {output_filename}")
                print(f"总处理时间: {time.time() - start_time:.2f} 秒")
                
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_tts_websocket()) 