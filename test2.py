import asyncio
import websockets
import json
import wave
import io
import time

async def test_tts_websocket():
    # WebSocket服务器地址
    uri = """ws://***************:9880/tts2?access_token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************.ZHZDzehyTymCuwtYMFc75LMxN9CHEyLOqCu59ByS3IRYLI7P186CZNR8S2r2b41zfzIaCafXJlbAVCQQTovSzTLfaf87VsQE1gB8RDPWngMOysYF2wTIvL6T2wn-y5XuDtjiliU1jrEdWSLFm5hXcy8M-3mY680tMmlWxkHuw-cF-1ZxkDRF4ipF5JwDdwseuJtLM2j8pLv_ryJus-Bb5TYRS-opUbUGco6gZUHcJktZUhIuYC5h0L_rjbh5peX6A-c2xFJX7Wp1Bu8afBTZTAw7RJuzk9IUURWHYJm4BlNC3NGyFS6579bx73r682S2ST0WWn_WLZGW55q8UMAFKg&per=wanyu_normal_zh_1"""
    start_time = time.time()
    print(f"开始时间: {start_time}")
    try:
        async with websockets.connect(uri) as websocket:
            t = {
                "type": "system.start",
                "payload": {
                    "spd": 5,
                    "pid": 5,
                    "vol": 5,
                    "audio_ctrl": "{\"sampling_rate\":16000}",
                    "aue": 3
                }
            }
            await websocket.send(json.dumps(t))
            message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
            print(f"message: {json.loads(message)}")
            # 准备测试数据
            test_data = {
                # "model": "cloudsway_tts",
                # "voice_id": "Luna_normal_1", 
                # "language": "en",
                "text": "你好啊，很高兴认识你",
                # "sample_rate": 32000,
                # "api_key": ""
            }
            test_data = {
                "type": "text",
                "payload": test_data
            }
            
            print("发送TTS请求...")
            # 发送请求
            await websocket.send(json.dumps(test_data))
            await websocket.send(json.dumps({"type":"system.finish",}))
            while True:
                message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                if type(message) == str:
                    print(message)
                    break
            await websocket.close()
                
    except Exception as e:
        print(f"发生错误: {e}")

    print(f"总处理时间: {time.time() - start_time:.2f} 秒")
    
if __name__ == "__main__":
    asyncio.run(test_tts_websocket())