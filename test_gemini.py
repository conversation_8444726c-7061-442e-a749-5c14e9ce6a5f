# from google import genai

# # The client gets the API key from the environment variable `GEMINI_API_KEY`.
# client = genai.Client()

# response = client.models.generate_content(
#     model="gemini-2.5-flash",
#     contents="Explain how AI works in a few words",
# )

# print(response.text)
import aiohttp
import nest_asyncio
import asyncio

nest_asyncio.apply()

async def fetch_data():
    payload = '{"api_key": "你的API密钥", "model": "cloudsway_tts", "text": "TEN Agent connected. How can I help you today?", "voice_id": "Luna_normal_1", "language": "en", "sample_rate": 32000}'
    headers = {
        'Content-Type': 'application/json'
    }
    url = 'http://116.148.216.100:9880/tts/stream'

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, data=payload) as response:
            print(response.content)

if __name__ == "__main__":
    asyncio.run(fetch_data())
