import wave

def convert_wav_to_pcm(wav_file, pcm_file):
    with open(wav_file, 'rb') as wf:
        wav_data = wf.read()
        
        # 去除前44个byte
        pcm_data = wav_data[44:]

        # 保存为pcm文件
        with open(pcm_file, 'wb') as pf:
            pf.write(pcm_data)

def wav_to_pcm(wav_path, pcm_path):
    with wave.open(wav_path, 'rb') as wav:
        params = wav.getparams()
        assert params.nchannels == 1 or 2  # 验证声道数
        frames = wav.readframes(params.nframes)
        with open(pcm_path, 'wb') as pcm:
            pcm.write(frames)
            
# 示例调用
wav_to_pcm('infer_2_moan_2.wav', 'output2.pcm')
